# VeditorInline Markdown样式禁用功能说明

## 功能概述

为 `VeditorInline` 组件添加了 `disableStyles` 参数，可以禁用Markdown语法的渲染和语法感应，让所有Markdown语法符号原样显示，但保留数学公式的渲染功能。

## 核心特性

1. **强制源码模式**: 禁用样式时自动切换到`sv`（源码）模式
2. **语法符号显示**: `#`、`**`、`-`等Markdown语法符号原样显示，不会被隐藏
3. **保留数学公式**: 只有`$...$`和`$$...$$`包围的数学公式会被渲染
4. **禁用其他渲染**: 标题、列表、粗体、斜体、代码块等都不会被渲染

## 新增参数

### disableStyles
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 控制是否禁用Markdown渲染样式
  - `false`: 正常渲染Markdown语法，使用用户指定的编辑器模式
  - `true`: 强制使用源码模式，禁用Markdown渲染，只保留数学公式

## 使用方法

### 基础用法

```vue
<template>
  <!-- 正常Markdown渲染编辑器 -->
  <VeditorInline
    v-model="content1"
    :height="200"
    :mode="ir"
    placeholder="正常Markdown渲染"
  />

  <!-- 禁用Markdown渲染编辑器（只保留数学公式） -->
  <VeditorInline
    v-model="content2"
    :height="200"
    :disableStyles="true"
    placeholder="只渲染数学公式，其他语法原样显示"
  />
</template>
```

### 动态切换样式

```vue
<template>
  <button @click="toggleStyles">
    {{ stylesDisabled ? '启用样式' : '禁用样式' }}
  </button>
  
  <VeditorInline 
    v-model="content"
    :disableStyles="stylesDisabled"
    placeholder="动态切换样式状态"
  />
</template>

<script setup>
import { ref } from 'vue'

const stylesDisabled = ref(false)
const content = ref('')

const toggleStyles = () => {
  stylesDisabled.value = !stylesDisabled.value
}
</script>
```

## 功能效果对比

### 正常模式 (`disableStyles: false`)
- **编辑器模式**: 使用用户指定的模式（ir/wysiwyg/sv）
- **语法处理**:
  - `# 标题` → 显示为大标题样式
  - `**粗体**` → 显示为粗体，语法符号可能被隐藏
  - `- 列表` → 显示为项目符号列表
  - `$E=mc^2$` → 渲染为数学公式
- **代码高亮**: 启用
- **所有渲染**: 启用

### 禁用样式模式 (`disableStyles: true`)
- **编辑器模式**: 强制使用源码模式（sv）
- **语法处理**:
  - `# 标题` → 原样显示 `# 标题`
  - `**粗体**` → 原样显示 `**粗体**`
  - `- 列表` → 原样显示 `- 列表`
  - `$E=mc^2$` → 仍然渲染为数学公式 ✓
- **代码高亮**: 禁用
- **Markdown渲染**: 禁用（除数学公式外）

## 保留的功能
- ✅ 数学公式渲染（`$...$` 和 `$$...$$`）
- ✅ 编辑器基本功能（输入、选择、撤销等）
- ✅ 编辑器容器样式（边框、高度等）
- ✅ XSS过滤

## 禁用的功能
- ❌ 标题样式渲染
- ❌ 文本样式（粗体、斜体、删除线）
- ❌ 列表样式渲染
- ❌ 代码块高亮
- ❌ 链接样式
- ❌ 表格样式
- ❌ 引用样式
- ❌ 语法符号隐藏

## 技术实现

### 1. 参数定义
```typescript
interface VeditorInlineProps {
  // ... 其他参数
  disableStyles?: boolean; // 控制是否禁用所有样式
}
```

### 2. 动态类绑定
```vue
<template>
  <div 
    :id="editorId" 
    :class="[
      'vditor-inline-container',
      { 'styles-disabled': props.disableStyles }
    ]"
  ></div>
</template>
```

### 3. CSS 样式覆盖
使用 `.styles-disabled` 类和 `!important` 规则覆盖所有原有样式，实现完全的样式重置。

### 4. 动态样式绑定
```css
:deep(.vditor-content) {
  height: v-bind('props.disableStyles ? "auto" : props.height + "px"') !important;
  overflow: v-bind('props.disableStyles ? "visible" : "hidden"');
}
```

## 测试验证

运行测试页面 `src/test-veditor-styles.vue` 可以验证功能：

1. 对比正常样式和禁用样式的编辑器
2. 测试动态切换样式状态
3. 查看编辑器内容的实时变化

## 注意事项

1. **第三方库样式**: 由于 `vditor/dist/index.css` 是第三方库样式，无法完全禁用，但通过 CSS 覆盖可以实现视觉上的样式禁用
2. **功能保持**: 禁用样式不影响编辑器的基本功能，仍可正常输入和编辑 Markdown 内容
3. **继承样式**: 禁用样式后，编辑器会继承父容器的字体、颜色等样式
4. **响应式**: 禁用样式后，编辑器高度变为自适应，会根据内容自动调整

## 兼容性

- 与现有所有参数兼容
- 不影响原有功能
- 向后兼容，默认值为 `false`
