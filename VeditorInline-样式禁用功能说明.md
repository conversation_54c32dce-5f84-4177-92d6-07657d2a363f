# VeditorInline Markdown样式禁用功能说明

## 功能概述

为 `VeditorInline` 组件添加了 `disableStyles` 参数，可以禁用Markdown语法的渲染样式，让标题、列表、粗体、斜体等Markdown语法显示为纯文本，但保持编辑器容器的样式不变。

## 新增参数

### disableStyles
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 控制是否禁用Markdown渲染样式
  - `false`: 正常渲染Markdown语法（标题显示为大字体、列表显示为项目符号等）
  - `true`: 禁用Markdown渲染样式（所有内容显示为普通文本）

## 使用方法

### 基础用法

```vue
<template>
  <!-- 正常样式编辑器 -->
  <VeditorInline 
    v-model="content1"
    :height="200"
    placeholder="正常样式编辑器"
  />
  
  <!-- 禁用样式编辑器 -->
  <VeditorInline 
    v-model="content2"
    :height="200"
    :disableStyles="true"
    placeholder="禁用样式编辑器"
  />
</template>
```

### 动态切换样式

```vue
<template>
  <button @click="toggleStyles">
    {{ stylesDisabled ? '启用样式' : '禁用样式' }}
  </button>
  
  <VeditorInline 
    v-model="content"
    :disableStyles="stylesDisabled"
    placeholder="动态切换样式状态"
  />
</template>

<script setup>
import { ref } from 'vue'

const stylesDisabled = ref(false)
const content = ref('')

const toggleStyles = () => {
  stylesDisabled.value = !stylesDisabled.value
}
</script>
```

## 样式禁用效果

当 `disableStyles` 为 `true` 时，以下样式将被禁用：

### 容器样式
- 边框和圆角
- 背景色
- 内边距和外边距

### 编辑器样式
- 固定高度（改为自适应）
- 内容区域样式
- 滚动条样式

### 内容样式
- 所有 Markdown 渲染样式
- 标题样式（h1-h6）
- 列表样式（ul, ol, li）
- 代码块样式（code, pre）
- 链接样式（a）
- 表格样式（table, th, td）
- 引用样式（blockquote）
- 字体样式（粗体、斜体等）

### 工具栏
- 完全隐藏工具栏

## 技术实现

### 1. 参数定义
```typescript
interface VeditorInlineProps {
  // ... 其他参数
  disableStyles?: boolean; // 控制是否禁用所有样式
}
```

### 2. 动态类绑定
```vue
<template>
  <div 
    :id="editorId" 
    :class="[
      'vditor-inline-container',
      { 'styles-disabled': props.disableStyles }
    ]"
  ></div>
</template>
```

### 3. CSS 样式覆盖
使用 `.styles-disabled` 类和 `!important` 规则覆盖所有原有样式，实现完全的样式重置。

### 4. 动态样式绑定
```css
:deep(.vditor-content) {
  height: v-bind('props.disableStyles ? "auto" : props.height + "px"') !important;
  overflow: v-bind('props.disableStyles ? "visible" : "hidden"');
}
```

## 测试验证

运行测试页面 `src/test-veditor-styles.vue` 可以验证功能：

1. 对比正常样式和禁用样式的编辑器
2. 测试动态切换样式状态
3. 查看编辑器内容的实时变化

## 注意事项

1. **第三方库样式**: 由于 `vditor/dist/index.css` 是第三方库样式，无法完全禁用，但通过 CSS 覆盖可以实现视觉上的样式禁用
2. **功能保持**: 禁用样式不影响编辑器的基本功能，仍可正常输入和编辑 Markdown 内容
3. **继承样式**: 禁用样式后，编辑器会继承父容器的字体、颜色等样式
4. **响应式**: 禁用样式后，编辑器高度变为自适应，会根据内容自动调整

## 兼容性

- 与现有所有参数兼容
- 不影响原有功能
- 向后兼容，默认值为 `false`
