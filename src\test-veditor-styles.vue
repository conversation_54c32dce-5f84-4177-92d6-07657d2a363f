<template>
  <div class="test-container">
    <h1>VeditorInline 样式禁用测试</h1>
    
    <div class="test-section">
      <h2>正常样式编辑器</h2>
      <VeditorInline 
        v-model="content1"
        :height="200"
        :showToolbar="true"
        placeholder="这是带样式的编辑器"
      />
    </div>
    
    <div class="test-section">
      <h2>禁用样式编辑器</h2>
      <VeditorInline 
        v-model="content2"
        :height="200"
        :showToolbar="true"
        :disableStyles="true"
        placeholder="这是禁用样式的编辑器"
      />
    </div>
    
    <div class="test-section">
      <h2>切换样式状态</h2>
      <button @click="toggleStyles">
        {{ stylesDisabled ? '启用样式' : '禁用样式' }}
      </button>
      <VeditorInline 
        v-model="content3"
        :height="200"
        :showToolbar="true"
        :disableStyles="stylesDisabled"
        placeholder="点击按钮切换样式状态"
      />
    </div>
    
    <div class="content-display">
      <h3>编辑器内容预览：</h3>
      <div class="content-box">
        <h4>编辑器1内容：</h4>
        <pre>{{ content1 }}</pre>
      </div>
      <div class="content-box">
        <h4>编辑器2内容：</h4>
        <pre>{{ content2 }}</pre>
      </div>
      <div class="content-box">
        <h4>编辑器3内容：</h4>
        <pre>{{ content3 }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import VeditorInline from './components/editors/VeditorInline.vue'

const content1 = ref('# 标题\n\n这是**粗体**文本和*斜体*文本。\n\n- 列表项1\n- 列表项2\n\n```javascript\nconsole.log("代码块");\n```')
const content2 = ref('# 标题\n\n这是**粗体**文本和*斜体*文本。\n\n- 列表项1\n- 列表项2\n\n```javascript\nconsole.log("代码块");\n```')
const content3 = ref('# 标题\n\n这是**粗体**文本和*斜体*文本。\n\n- 列表项1\n- 列表项2\n\n```javascript\nconsole.log("代码块");\n```')

const stylesDisabled = ref(false)

const toggleStyles = () => {
  stylesDisabled.value = !stylesDisabled.value
}
</script>

<style scoped>
.test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

button {
  padding: 10px 20px;
  margin-bottom: 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

button:hover {
  background-color: #0056b3;
}

.content-display {
  margin-top: 40px;
  padding: 20px;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.content-box {
  margin-bottom: 20px;
  padding: 15px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.content-box h4 {
  margin-top: 0;
  color: #555;
}

pre {
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
