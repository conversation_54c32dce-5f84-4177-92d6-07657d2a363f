<template>
  <div class="test-container">
    <h1>VeditorInline Markdown样式禁用测试</h1>

    <div class="test-section">
      <h2>正常Markdown渲染编辑器</h2>
      <p>标题、列表、粗体、斜体等Markdown语法会正常渲染，数学公式也会渲染</p>
      <VeditorInline
        v-model="content1"
        :height="250"
        :showToolbar="true"
        placeholder="输入Markdown语法，会正常渲染样式"
      />
    </div>

    <div class="test-section">
      <h2>禁用Markdown渲染编辑器（只保留数学公式）</h2>
      <p>强制使用源码模式，Markdown语法符号原样显示，但数学公式仍会渲染</p>
      <VeditorInline
        v-model="content2"
        :height="250"
        :showToolbar="true"
        :disableStyles="true"
        placeholder="Markdown语法原样显示，只渲染数学公式"
      />
    </div>

    <div class="test-section">
      <h2>动态切换渲染模式</h2>
      <p>点击按钮可以动态切换Markdown渲染的启用/禁用状态</p>
      <button @click="toggleStyles">
        {{ stylesDisabled ? '启用Markdown渲染' : '禁用Markdown渲染（只保留数学公式）' }}
      </button>
      <VeditorInline
        v-model="content3"
        :height="250"
        :showToolbar="true"
        :disableStyles="stylesDisabled"
        placeholder="点击按钮切换渲染模式"
      />
    </div>
    
    <div class="content-display">
      <h3>编辑器内容预览：</h3>
      <div class="content-box">
        <h4>编辑器1内容：</h4>
        <pre>{{ content1 }}</pre>
      </div>
      <div class="content-box">
        <h4>编辑器2内容：</h4>
        <pre>{{ content2 }}</pre>
      </div>
      <div class="content-box">
        <h4>编辑器3内容：</h4>
        <pre>{{ content3 }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import VeditorInline from './components/editors/VeditorInline.vue'

const content1 = ref(`# 一级标题
## 二级标题
### 三级标题

这是**粗体**文本和*斜体*文本，还有~~删除线~~。

- 无序列表项1
- 无序列表项2
  - 嵌套列表项

1. 有序列表项1
2. 有序列表项2

> 这是引用文本
> 可以多行

\`行内代码\`

\`\`\`javascript
// 代码块
console.log("Hello World");
\`\`\`

数学公式测试：
行内公式：$E = mc^2$
块级公式：
$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$

[链接文本](https://example.com)

---

| 表格 | 列1 | 列2 |
|------|-----|-----|
| 行1  | 数据1 | 数据2 |`)

const content2 = ref(`# 一级标题
## 二级标题
### 三级标题

这是**粗体**文本和*斜体*文本，还有~~删除线~~。

- 无序列表项1
- 无序列表项2
  - 嵌套列表项

1. 有序列表项1
2. 有序列表项2

> 这是引用文本
> 可以多行

\`行内代码\`

\`\`\`javascript
// 代码块
console.log("Hello World");
\`\`\`

数学公式测试：
行内公式：$E = mc^2$
块级公式：
$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$

[链接文本](https://example.com)

---

| 表格 | 列1 | 列2 |
|------|-----|-----|
| 行1  | 数据1 | 数据2 |`)

const content3 = ref(`# 一级标题
## 二级标题
### 三级标题

这是**粗体**文本和*斜体*文本，还有~~删除线~~。

- 无序列表项1
- 无序列表项2
  - 嵌套列表项

1. 有序列表项1
2. 有序列表项2

> 这是引用文本
> 可以多行

\`行内代码\`

\`\`\`javascript
// 代码块
console.log("Hello World");
\`\`\`

数学公式测试：
行内公式：$E = mc^2$
块级公式：
$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$

[链接文本](https://example.com)

---

| 表格 | 列1 | 列2 |
|------|-----|-----|
| 行1  | 数据1 | 数据2 |`)

const stylesDisabled = ref(false)

const toggleStyles = () => {
  stylesDisabled.value = !stylesDisabled.value
}
</script>

<style scoped>
.test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

button {
  padding: 10px 20px;
  margin-bottom: 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

button:hover {
  background-color: #0056b3;
}

.content-display {
  margin-top: 40px;
  padding: 20px;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.content-box {
  margin-bottom: 20px;
  padding: 15px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.content-box h4 {
  margin-top: 0;
  color: #555;
}

pre {
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
